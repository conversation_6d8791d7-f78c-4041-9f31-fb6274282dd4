# Composio Backend Usage Analysis

## Overview

This document provides a comprehensive analysis of all backend files that import, reference, or actively use Composio functionality in the Atlas codebase. Each file is analyzed with specific code snippets, import statements, and usage patterns.

---

## 📁 **Core API Files**

### 1. **`backend/api.py`** - Main API Router

**Breadcrumb:** `backend/api.py`
**Purpose:** Main FastAPI application that includes all API routers
**Composio Usage:** Imports and registers both old MCP and new v3 Composio API routers

**Code Snippets:**

```python
# Lines 183-187: Router imports and registration
from composio_mcp import router as composio_mcp_router
from composio_v3_api import router as composio_v3_router

app.include_router(composio_mcp_router, prefix="/api")
app.include_router(composio_v3_router, prefix="/api")
```

**Impact:** This is the main entry point that exposes all Composio endpoints to the frontend.

---

### 2. **`backend/api/composio_mcp.py`** - Legacy MCP API Endpoints

**Breadcrumb:** `backend/api/composio_mcp.py`
**Purpose:** FastAPI endpoints for the old Composio MCP integration using constants-based URLs
**Composio Usage:** Heavily uses Composio MCP services for connection management, tool discovery, and authentication

**Code Snippets:**

```python
# Lines 13-19: Service imports
from services.composio_integration import (
    composio_mcp_service,
    ComposioMCPConnection,
    composio_tool_executor,
)
from constants.composio_mcp_constants import get_supported_composio_apps

# Lines 145, 188, 216, 354, 410, 471, 524, 554, 557: Active service usage
connection = await composio_mcp_service.create_user_mcp_connection_no_storage(...)
connections = await composio_mcp_service.list_user_mcp_connections(user_id)
deleted = await composio_mcp_service.delete_user_mcp_connection(...)
success = await composio_mcp_service.update_mcp_enabled_tools(...)
result = await composio_tool_executor.execute_initiate_connection_tool(...)
```

**Impact:** Provides 11 API endpoints for legacy MCP integration including connection management, tool discovery, and authentication.

---

### 3. **`backend/api/composio_v3_api.py`** - New V3 API Endpoints

**Breadcrumb:** `backend/api/composio_v3_api.py`
**Purpose:** FastAPI endpoints for the new Composio v3 SDK integration using entity IDs
**Composio Usage:** Uses the new v3 service for cleaner authentication flow

**Code Snippets:**

```python
# Line 20: Service import
from services.composio_v3_service import composio_v3_service

# Lines 131, 189, 242, 270, 311, 343, 390: Active service usage
connection_result = await composio_v3_service.initiate_connection(...)
auth_result = await composio_v3_service.wait_for_connection(...)
connections = await composio_v3_service.list_user_connections(user_id)
success = await composio_v3_service.delete_user_connection(user_id, app_key)
integrations = await composio_v3_service.get_supported_integrations()
```

**Impact:** Provides 8 API endpoints for the new v3 integration with cleaner SDK-based authentication.

---

## 🔧 **Service Layer Files**

### 4. **`backend/services/composio_integration.py`** - Legacy MCP Service Implementation

**Breadcrumb:** `backend/services/composio_integration.py`
**Purpose:** Core service implementing the old Composio MCP integration with constants-based URL generation
**Composio Usage:** Implements ComposioMCPService class and ComposioMCPToolExecutor class

**Code Snippets:**

```python
# Lines 28-31: Constants import
from constants.composio_mcp_constants import (
    get_composio_mcp_url,
    is_composio_app_supported,
)

# Line 320: Main service class
class ComposioMCPService:
    # Implements connection management, tool discovery, authentication

# Line 1431: Service instance
composio_mcp_service = ComposioMCPService()

# Lines 1761+: Tool executor class
class ComposioMCPToolExecutor:
    # Implements dynamic tool execution on Composio MCP servers
```

**Impact:** 2068+ lines of core MCP integration logic including connection management, OAuth flow, and tool execution.

---

### 5. **`backend/services/composio_v3_service.py`** - New V3 Service Implementation

**Breadcrumb:** `backend/services/composio_v3_service.py`
**Purpose:** Core service implementing the new Composio v3 SDK integration using entity IDs
**Composio Usage:** Direct import and usage of the official Composio Python SDK

**Code Snippets:**

```python
# Lines 32-33: SDK imports
import composio
from composio.client.exceptions import ComposioClientError

# Line 89: SDK initialization
from composio import Composio

# Line 71: Main service class
class ComposioV3Service:
    # Implements v3 SDK-based authentication and connection management

# Line 562: Service instance
composio_v3_service = ComposioV3Service()
```

**Impact:** 563 lines of v3 SDK integration logic with cleaner authentication flow using official SDK.

---

### 6. **`backend/services/tool_information_fetcher.py`** - Tool Information Service

**Breadcrumb:** `backend/services/tool_information_fetcher.py`
**Purpose:** Service that fetches detailed tool information from Composio MCP servers before agent execution
**Composio Usage:** Uses ComposioMCPService to fetch tool information for agent execution

**Code Snippets:**

```python
# Line 13: Service import
from services.composio_integration import ComposioMCPService

# Line 23: Service instantiation
self.composio_service = ComposioMCPService()

# Lines 41, 47-51, 93: Active usage
composio_mentions = [m for m in mentions if m.type == "composio_mcp"]
if composio_mentions:
    composio_info = await self._fetch_composio_tool_info(composio_mentions, user_id)
    tool_info_list.extend(composio_info)

connection = await self.composio_service.create_user_mcp_connection_no_storage(...)
```

**Impact:** Critical service that enables agents to discover and use Composio tools during execution.

---

## 🔧 **Utility Files**

### 7. **`backend/utils/tool_mention_processor.py`** - Tool Mention Processing

**Breadcrumb:** `backend/utils/tool_mention_processor.py`
**Purpose:** Processes tool mentions in agent messages and categorizes them by type
**Composio Usage:** Contains Composio-specific logic for detecting and processing Composio tool mentions

**Code Snippets:**

```python
# Line 21: Type definition includes composio_mcp
type: str  # 'configured_mcp' | 'custom_mcp' | 'composio_mcp'

# Lines 105-106: Composio detection logic
elif "composio" in tool_id or tool_id.startswith("available_composio_"):
    return "composio_mcp"

# Line 122: Composio-specific method
def extract_composio_app_keys(self, mentions: List[ParsedToolMention]) -> List[str]:

# Lines 135, 142, 156: Composio processing logic
if mention.type == "composio_mcp":
    # Expected formats: 'available_composio_gmail', 'composio_slack', etc.
cleaned_id = tool_id.replace("available_composio_", "").replace("composio_", "")
```

**Impact:** Essential utility for parsing and categorizing Composio tool mentions in agent conversations.

---

### 8. **`backend/utils/dynamic_tool_prompt.py`** - Dynamic Tool Prompt Generation

**Breadcrumb:** `backend/utils/dynamic_tool_prompt.py`
**Purpose:** Generates dynamic tool prompts for agents with tool descriptions
**Composio Usage:** References composio_mcp_servers.json for tool descriptions

**Code Snippets:**

```python
# Line 209: Function docstring
"""Get user's configured tools with descriptions from composio_mcp_servers.json."""

# Line 231: Comment about loading descriptions
# Load tool descriptions from composio_mcp_servers.json

# Lines 270, 275: File path reference
"""Load tool descriptions from composio_mcp_servers.json."""
"composio_mcp_servers.json",
```

**Impact:** Uses Composio tool descriptions to generate better prompts for agent tool usage.

---

### 9. **`backend/utils/config.py`** - Configuration Management

**Breadcrumb:** `backend/utils/config.py`
**Purpose:** Application configuration management
**Composio Usage:** Defines Composio API base URL configuration

**Code Snippets:**

```python
# Line 138: Composio API configuration
COMPOSIO_API_BASE: Optional[str] = "https://backend.composio.dev/api/v3"
```

**Impact:** Provides configuration for Composio API endpoints.

---

## 📄 **Constants and Data Files**

### 10. **`backend/constants/composio_mcp_constants.py`** - MCP Constants and Utilities

**Breadcrumb:** `backend/constants/composio_mcp_constants.py`
**Purpose:** Provides constants and utilities for working with Composio MCP servers
**Composio Usage:** Core constants file for MCP URL generation and app validation

**Code Snippets:**

```python
# Lines 15-17: JSON file reference
CONSTANTS_FILE_PATH = os.path.join(
    os.path.dirname(__file__), "composio_mcp_servers.json"
)

# Lines 121-155: Convenience functions
def get_composio_mcp_url(app_key: str, user_id: str) -> Optional[str]:
def get_supported_composio_apps() -> List[str]:
def is_composio_app_supported(app_key: str) -> bool:
```

**Impact:** 156 lines of constants and utilities for MCP URL generation and app validation.

---

### 11. **`backend/constants/composio_mcp_servers.json`** - MCP Server Configuration

**Breadcrumb:** `backend/constants/composio_mcp_servers.json`
**Purpose:** JSON configuration file containing Composio MCP server URLs, integration IDs, and tool definitions
**Composio Usage:** Core data file with 1294 lines defining 18 supported apps and their tools

**Content Overview:**

- **18 supported apps:** gmail, notion, teams, twitter, linear, google_docs, slack, google_calendar, google_sheets, airtable, reddit, outlook, hubspot, zoom, salesforce, google_drive, clickup
- **Each app contains:** integration_id, server_id, name, description, tools array
- **Total tools:** 300+ individual tool definitions across all apps

**Impact:** Critical data file that defines all supported Composio integrations and their available tools.

---

## 📦 **Dependencies**

### 12. **`backend/pyproject.toml`** - Python Dependencies

**Breadcrumb:** `backend/pyproject.toml`
**Purpose:** Python package dependencies configuration
**Composio Usage:** Includes Composio SDK as a dependency

**Code Snippets:**

```toml
# Line 64: Composio SDK dependency
"composio",
```

**Impact:** Ensures the Composio Python SDK is installed for v3 service functionality.

---

## 🧪 **Test Files**

### 13. **`backend/test_tool_mentions.py`** - Tool Mention Tests

**Breadcrumb:** `backend/test_tool_mentions.py`
**Purpose:** Unit tests for tool mention processing functionality
**Composio Usage:** Contains test cases with Composio tool mentions

**Code Snippets:**

```python
# Lines 31-32, 35: Test data with Composio mentions
"Can you help me send an email using @[Gmail](available_composio_gmail)?",
"Please use @[Slack](available_composio_slack) and @[Notion](available_composio_notion) to organize this.",
"Multiple @[Gmail](available_composio_gmail) mentions @[Gmail](available_composio_gmail) in one message.",

# Lines 69, 110, 148: More test cases
test_message = "Please use @[Gmail](available_composio_gmail) to send an email."
test_message = "Use @[Gmail](available_composio_gmail) to send emails and @[Slack](available_composio_slack) for notifications."
"content": "Please use @[Gmail](available_composio_gmail) to send an important email."
```

**Impact:** Ensures Composio tool mention processing works correctly.

---

### 14. **`backend/test_api_tool_mentions.py`** - API Tool Mention Tests

**Breadcrumb:** `backend/test_api_tool_mentions.py`
**Purpose:** API integration tests for tool mention processing
**Composio Usage:** Contains API test cases with Composio tool mentions

**Code Snippets:**

```python
# Line 66: Test message with Composio mentions
test_message_content = "Please use @[Gmail](available_composio_gmail) to send an email and @[Slack](available_composio_slack) for notifications."
```

**Impact:** Ensures API endpoints correctly handle Composio tool mentions.

---

## 📊 **Summary**

### **Files That MUST Be Kept (Active V3 Usage):**

1. `backend/api/composio_v3_api.py` - New V3 API endpoints
2. `backend/services/composio_v3_service.py` - V3 service implementation
3. `backend/pyproject.toml` - Contains Composio SDK dependency

### **Files That CAN Be Removed (Legacy MCP Only):**

1. `backend/api/composio_mcp.py` - Legacy MCP API endpoints
2. `backend/services/composio_integration.py` - Legacy MCP service implementation
3. `backend/constants/composio_mcp_constants.py` - MCP constants and utilities
4. `backend/constants/composio_mcp_servers.json` - MCP server configuration

### **Files That Need Updates (Mixed Usage):**

1. `backend/api.py` - Remove MCP router import/registration
2. `backend/services/tool_information_fetcher.py` - Update to use V3 service
3. `backend/utils/tool_mention_processor.py` - Update Composio detection logic
4. `backend/utils/dynamic_tool_prompt.py` - Update to use V3 tool descriptions
5. `backend/utils/config.py` - Keep V3 API base URL

### **Test Files (Can Be Removed):**

1. `backend/test_tool_mentions.py` - Contains Composio test cases
2. `backend/test_api_tool_mentions.py` - Contains Composio API test cases

**Total Impact:** 14 files with varying levels of Composio integration, ranging from core service implementations (2000+ lines) to simple configuration references (single lines).

---

## 🔍 **Verification Complete**

This analysis was conducted through comprehensive grep operations across the entire backend codebase to ensure no Composio references were missed. All files containing "composio" (case-insensitive) have been identified and analyzed with specific code snippets and usage patterns.

**Grep Commands Used:**

- `find backend -type f -name "*.py" -exec grep -l -i "composio" {} \;`
- `grep -r "import.*composio\|from.*composio" backend/ --include="*.py"`
- `grep -r "ComposioMCPService\|ComposioV3Service\|composio_mcp_service\|composio_v3_service\|composio_tool_executor" backend/ --include="*.py" -n`
- `find . -name "*composio*" -type f`
- Multiple targeted searches for specific patterns and usage

**Files Analyzed:** 14 total files with Composio references
**Lines of Code:** 4000+ lines directly related to Composio functionality
**Integration Depth:** From core service implementations to configuration and test files
