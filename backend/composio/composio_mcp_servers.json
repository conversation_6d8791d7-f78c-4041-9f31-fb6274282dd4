{"gmail": {"integration_id": "096a940c-830d-49f9-be93-c4a5f56fd033", "server_id": "68d33e3f-af97-45ae-9a36-7870b4618dc8", "name": "Gmail", "description": "Connect to Gmail for email management and automation", "tools": [{"name": "GMAIL_DELETE_DRAFT", "description": "Delete a draft email from Gmail"}, {"name": "GMAIL_DELETE_MESSAGE", "description": "Delete a message from Gmail"}, {"name": "GMAIL_FETCH_EMAILS", "description": "Fetch emails from Gmail with various filters"}, {"name": "GMAIL_GET_CONTACTS", "description": "Get contacts from Gmail address book"}, {"name": "GMAIL_LIST_DRAFTS", "description": "List all draft emails in Gmail"}, {"name": "GMAIL_MOVE_TO_TRASH", "description": "Move an email to trash in Gmail"}, {"name": "GMAIL_PATCH_LABEL", "description": "Update labels on Gmail messages"}, {"name": "GMAIL_REPLY_TO_THREAD", "description": "Reply to an email thread in Gmail"}, {"name": "GMAIL_SEARCH_PEOPLE", "description": "Search for people in Gmail contacts"}, {"name": "GMAIL_SEND_DRAFT", "description": "Send a draft email from Gmail"}, {"name": "GMAIL_SEND_EMAIL", "description": "Send a new email via Gmail"}, {"name": "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID", "description": "Fetch a specific message by ID from Gmail"}, {"name": "GMAIL_CREATE_EMAIL_DRAFT", "description": "Create a new email draft in Gmail"}, {"name": "GMAIL_FETCH_MESSAGE_BY_THREAD_ID", "description": "Fetch messages from a specific thread in Gmail"}, {"name": "GMAIL_LIST_THREADS", "description": "List email threads in Gmail"}]}, "notion": {"integration_id": "04a8a7a5-b159-4f16-82be-4c4b786b3689", "server_id": "8476a4d4-0d7f-4c1b-9509-b9b358e841d1", "name": "Notion", "description": "Connect to Notion for productivity and note management", "tools": [{"name": "NOTION_ADD_PAGE_CONTENT", "description": "Add content to an existing Notion page"}, {"name": "NOTION_CREATE_DATABASE", "description": "Create a new database in Notion"}, {"name": "NOTION_CREATE_NOTION_PAGE", "description": "Create a new page in Notion"}, {"name": "NOTION_DELETE_BLOCK", "description": "Delete a block from a Notion page"}, {"name": "NOTION_FETCH_DATA", "description": "Fetch data from Notion"}, {"name": "NOTION_FETCH_DATABASE", "description": "Fetch database information from Notion"}, {"name": "NOTION_FETCH_ROW", "description": "Fetch a specific row from a Notion database"}, {"name": "NOTION_GET_ABOUT_USER", "description": "Get information about a Notion user"}, {"name": "NOTION_INSERT_ROW_DATABASE", "description": "Insert a new row into a Notion database"}, {"name": "NOTION_LIST_USERS", "description": "List all users in a Notion workspace"}, {"name": "NOTION_QUERY_DATABASE", "description": "Query a Notion database with filters"}, {"name": "NOTION_RETRIEVE_COMMENT", "description": "Retrieve comments from a Notion page"}, {"name": "NOTION_RETRIEVE_DATABASE_PROPERTY", "description": "Retrieve properties of a Notion database"}, {"name": "NOTION_UPDATE_PAGE", "description": "Update an existing Notion page"}, {"name": "NOTION_UPDATE_SCHEMA_DATABASE", "description": "Update the schema of a Notion database"}, {"name": "NOTION_APPEND_BLOCK_CHILDREN", "description": "Append child blocks to a Notion page"}, {"name": "NOTION_FETCH_NOTION_BLOCK", "description": "Fetch a specific block from Notion"}, {"name": "NOTION_FETCH_NOTION_CHILD_BLOCK", "description": "Fetch child blocks from a Notion page"}, {"name": "NOTION_GET_PAGE_PROPERTY_ACTION", "description": "Get specific properties from a Notion page"}, {"name": "NOTION_NOTION_UPDATE_BLOCK", "description": "Update a block in a Notion page"}, {"name": "NOTION_SEARCH_NOTION_PAGE", "description": "Search for pages in Notion"}]}, "teams": {"integration_id": "e3fe467f-5a28-4e42-a6c9-6117e67afd92", "server_id": "3bbab4f9-c610-4d3a-acbc-f71439d401fb", "name": "Microsoft Teams", "description": "Connect to Microsoft Teams for collaboration and communication", "tools": [{"name": "MICROSOFT_TEAMS_CREATE_MEETING", "description": "Create a new meeting in Microsoft Teams"}, {"name": "MICROSOFT_TEAMS_TEAMS_CREATE_CHAT", "description": "Create a new chat in Microsoft Teams"}, {"name": "MICROSOFT_TEAMS_CHATS_GET_ALL_CHATS", "description": "Get all chats in Microsoft Teams"}, {"name": "MICROSOFT_TEAMS_CHATS_GET_ALL_MESSAGES", "description": "Get all messages from a Teams chat"}, {"name": "MICROSOFT_TEAMS_TEAMS_CREATE_CHANNEL", "description": "Create a new channel in a Teams team"}, {"name": "MICROSOFT_TEAMS_TEAMS_GET_MESSAGE", "description": "Get a specific message from Teams"}, {"name": "MICROSOFT_TEAMS_TEAMS_LIST", "description": "List all Teams in the organization"}, {"name": "MICROSOFT_TEAMS_TEAMS_LIST_CHANNELS", "description": "List channels in a specific Team"}, {"name": "MICROSOFT_TEAMS_TEAMS_LIST_CHAT_MESSAGES", "description": "List messages in a Teams chat"}, {"name": "MICROSOFT_TEAMS_TEAMS_LIST_PEOPLE", "description": "List people in a Team or chat"}, {"name": "MICROSOFT_TEAMS_TEAMS_POST_CHANNEL_MESSAGE", "description": "Post a message to a Teams channel"}, {"name": "MICROSOFT_TEAMS_TEAMS_POST_CHAT_MESSAGE", "description": "Post a message to a Teams chat"}, {"name": "MICROSOFT_TEAMS_TEAMS_POST_MESSAGE_REPLY", "description": "Reply to a message in Teams"}]}, "twitter": {"integration_id": "1151c730-1f24-46b7-89d2-7694d99dc18c", "server_id": "d09b2a35-3e68-4160-a9e5-ed7cac357b6e", "name": "Twitter", "description": "Connect to Twitter for social media management and automation", "tools": [{"name": "TWITTER_CREATE_LIST", "description": "Create a new Twitter list"}, {"name": "TWITTER_CREATION_OF_A_POST", "description": "Create a new post on Twitter"}, {"name": "TWITTER_DELETE_LIST", "description": "Delete a Twitter list"}, {"name": "TWITTER_FOLLOW_USER", "description": "Follow a user on Twitter"}, {"name": "TWITTER_GET_BLOCKED_USERS", "description": "Get list of blocked users on Twitter"}, {"name": "TWITTER_POST_DELETE_BY_POST_ID", "description": "Delete a post by ID on Twitter"}, {"name": "TWITTER_UNFOLLOW_USER", "description": "Unfollow a user on Twitter"}, {"name": "TWITTER_USER_LOOKUP_BY_USERNAME", "description": "Look up a Twitter user by username"}, {"name": "TWITTER_USER_LOOKUP_ME", "description": "Get current user's Twitter profile"}, {"name": "TWITTER_CREATE_A_NEW_DM_CONVERSATION", "description": "Create a new DM conversation on Twitter"}, {"name": "TWITTER_RETWEET_POST", "description": "Retweet a post on Twitter"}, {"name": "TWITTER_USER_LIKE_POST", "description": "Like a post on Twitter"}, {"name": "TWITTER_FOLLOW_A_LIST", "description": "Follow a Twitter list"}]}, "linear": {"integration_id": "0c964f51-0adb-4d7e-bbbd-a7d5a2ca5756", "server_id": "dc8c1561-e394-46e3-9072-d4b8cd371aa3", "name": "Linear", "description": "Connect to Linear for issue tracking and project management", "tools": [{"name": "LINEAR_REMOVE_ISSUE_LABEL", "description": "Remove a label from a Linear issue"}, {"name": "LINEAR_UPDATE_ISSUE", "description": "Update an existing Linear issue"}, {"name": "LINEAR_CREATE_LINEAR_COMMENT", "description": "Create a comment on a Linear issue"}, {"name": "LINEAR_CREATE_LINEAR_ISSUE", "description": "Create a new issue in Linear"}, {"name": "LINEAR_CREATE_LINEAR_LABEL", "description": "Create a new label in Linear"}, {"name": "LINEAR_DELETE_LINEAR_ISSUE", "description": "Delete an issue from Linear"}, {"name": "LINEAR_GET_LINEAR_ISSUE", "description": "Get details of a specific Linear issue"}, {"name": "LINEAR_LIST_LINEAR_CYCLES", "description": "List all cycles in Linear"}, {"name": "LINEAR_LIST_LINEAR_ISSUES", "description": "List all issues in Linear"}, {"name": "LINEAR_LIST_LINEAR_LABELS", "description": "List all labels in Linear"}, {"name": "LINEAR_LIST_LINEAR_PROJECTS", "description": "List all projects in Linear"}, {"name": "LINEAR_LIST_LINEAR_STATES", "description": "List all workflow states in Linear"}, {"name": "LINEAR_LIST_LINEAR_TEAMS", "description": "List all teams in Linear"}, {"name": "LINEAR_RUN_QUERY_OR_MUTATION", "description": "Run custom GraphQL query or mutation in Linear"}, {"name": "LINEAR_CREATE_LINEAR_ATTACHMENT", "description": "Create an attachment on a Linear issue"}]}, "google_docs": {"integration_id": "516f6578-9ba6-4bc9-87f3-311799d4ae15", "server_id": "e1a27896-41fe-40bf-81db-5a29c3c46310", "name": "Google Docs", "description": "Connect to Google Docs for document creation and collaboration", "tools": [{"name": "GOOGLEDOCS_COPY_DOCUMENT", "description": "Create a copy of a Google Doc"}, {"name": "GOOGLEDOCS_CREATE_FOOTER", "description": "Create a footer in a Google Doc"}, {"name": "GOOGLEDOCS_CREATE_HEADER", "description": "Create a header in a Google Doc"}, {"name": "GOOGLEDOCS_CREATE_NAMED_RANGE", "description": "Create a named range in a Google Doc"}, {"name": "GOOGLEDOCS_CREATE_PARAGRAPH_BULLETS", "description": "Create bulleted paragraphs in a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_CONTENT_RANGE", "description": "Delete content range in a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_FOOTER", "description": "Delete footer from a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_HEADER", "description": "Delete header from a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_NAMED_RANGE", "description": "Delete a named range in a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_PARAGRAPH_BULLETS", "description": "Remove bullets from paragraphs in a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_TABLE", "description": "Delete a table from a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_TABLE_COLUMN", "description": "Delete a table column in a Google Doc"}, {"name": "GOOGLEDOCS_DELETE_TABLE_ROW", "description": "Delete a table row in a Google Doc"}, {"name": "GOOGLEDOCS_GET_CHARTS_FROM_SPREADSHEET", "description": "Get charts from a Google Spreadsheet"}, {"name": "GOOGLEDOCS_INSERT_PAGE_BREAK", "description": "Insert a page break in a Google Doc"}, {"name": "GOOGLEDOCS_INSERT_TABLE_ACTION", "description": "Insert a table in a Google Doc"}, {"name": "GOOGLEDOCS_INSERT_TEXT_ACTION", "description": "Insert text in a Google Doc"}, {"name": "GOOGLEDOCS_REPLACE_ALL_TEXT", "description": "Replace all occurrences of text in a Google Doc"}, {"name": "GOOGLEDOCS_UPDATE_DOCUMENT_STYLE", "description": "Update document style in a Google Doc"}, {"name": "GOOGLEDOCS_CREATE_DOCUMENT", "description": "Create a new Google Doc"}, {"name": "GOOGLEDOCS_GET_DOCUMENT_BY_ID", "description": "Get a Google Doc by its ID"}, {"name": "GOOGLEDOCS_SEARCH_DOCUMENTS", "description": "Search for Google Docs"}]}, "slack": {"integration_id": "68ff60d9-c1f3-4dcc-a97b-c25ba6c7851b", "server_id": "c88a2c5a-ed81-4239-89ab-b395010ad899", "name": "<PERSON><PERSON>ck", "description": "Connect to Slack for team communication and workflow automation", "tools": [{"name": "SLACK_ADD_REACTION_TO_AN_ITEM", "description": "Add a reaction to a Slack message"}, {"name": "SLACK_CREATE_A_REMINDER", "description": "Create a reminder in Slack"}, {"name": "SLACK_FETCH_CONVERSATION_HISTORY", "description": "Fetch conversation history from a Slack channel"}, {"name": "SLACK_LIST_ALL_SLACK_TEAM_CHANNELS_WITH_VARIOUS_FILTERS", "description": "List all team channels with filters"}, {"name": "SLACK_LIST_ALL_SLACK_TEAM_USERS_WITH_PAGINATION", "description": "List all team users with pagination"}, {"name": "SLACK_REMOVE_REACTION_FROM_ITEM", "description": "Remove a reaction from a Slack message"}, {"name": "SLACK_SCHEDULES_A_MESSAGE_TO_A_CHANNEL_AT_A_SPECIFIED_TIME", "description": "Schedule a message to be sent later"}, {"name": "SLACK_SEARCH_FOR_MESSAGES_WITH_QUERY", "description": "Search for messages in Slack"}, {"name": "SLACK_SENDS_A_MESSAGE_TO_A_SLACK_CHANNEL", "description": "Send a message to a Slack channel"}, {"name": "SLACK_UPDATES_A_SLACK_MESSAGE", "description": "Update an existing Slack message"}, {"name": "SLACK_CONVERSATIONS_HISTORY", "description": "Get conversation history"}, {"name": "SLACK_CONVERSATIONS_INFO", "description": "Get information about a conversation"}, {"name": "SLACK_CONVERSATIONS_LIST", "description": "List conversations"}, {"name": "SLACK_USERS_INFO", "description": "Get information about a user"}, {"name": "SLACK_USERS_PROFILE_GET_PROFILE_INFO", "description": "Get user profile information"}, {"name": "SLACK_LISTS_PINNED_ITEMS_IN_A_CHANNEL", "description": "List pinned items in a channel"}]}, "google_calendar": {"integration_id": "736758e6-c9b4-41dc-af38-e2e3eef3f466", "server_id": "f8d4c979-919b-4a68-9277-e0f2181f96f6", "name": "Google Calendar", "description": "Connect to Google Calendar for schedule management and event automation", "tools": [{"name": "GOOGLECALENDAR_PATCH_EVENT", "description": "Partially update a calendar event"}, {"name": "GOOGLECALENDAR_CALENDARS_UPDATE", "description": "Update calendar settings"}, {"name": "GOOGLECALENDAR_CLEAR_CALENDAR", "description": "Clear all events from a calendar"}, {"name": "GOOGLECALENDAR_CREATE_EVENT", "description": "Create a new calendar event"}, {"name": "GOOGLECALENDAR_DELETE_EVENT", "description": "Delete a calendar event"}, {"name": "GOOGLECALENDAR_DUPLICATE_CALENDAR", "description": "Duplicate an entire calendar"}, {"name": "GOOGLECALENDAR_EVENTS_INSTANCES", "description": "Get instances of a recurring event"}, {"name": "GOOGLECALENDAR_EVENTS_LIST", "description": "List calendar events"}, {"name": "GOOGLECALENDAR_EVENTS_MOVE", "description": "Move an event to another calendar"}, {"name": "GOOGLECALENDAR_FIND_EVENT", "description": "Find specific events in calendar"}, {"name": "GOOGLECALENDAR_GET_CALENDAR", "description": "Get calendar information"}, {"name": "GOOGLECALENDAR_GET_CURRENT_DATE_TIME", "description": "Get current date and time"}, {"name": "GOOGLECALENDAR_LIST_CALENDARS", "description": "List all calendars"}, {"name": "GOOGLECALENDAR_QUICK_ADD", "description": "Quick add event with natural language"}, {"name": "GOOGLECALENDAR_REMOVE_ATTENDEE", "description": "Remove an attendee from an event"}, {"name": "GOOGLECALENDAR_UPDATE_EVENT", "description": "Update an existing calendar event"}, {"name": "GOOGLECALENDAR_FIND_FREE_SLOTS", "description": "Find free time slots in calendar"}]}, "google_sheets": {"integration_id": "d00ad113-12e4-4807-92de-bfeca3831474", "server_id": "3cb65ed0-c88b-45b5-a140-7d81609136d2", "name": "Google Sheets", "description": "Connect to Google Sheets for spreadsheet automation and data management", "tools": [{"name": "GOOGLESHEETS_ADD_SHEET", "description": "Add a new sheet to a spreadsheet"}, {"name": "GOOGLESHEETS_CLEAR_BASIC_FILTER", "description": "Clear basic filter from a sheet"}, {"name": "GOOGLESHEETS_DELETE_SHEET", "description": "Delete a sheet from a spreadsheet"}, {"name": "GOOGLESHEETS_GET_SPREADSHEET_BY_DATA_FILTER", "description": "Get spreadsheet data using filters"}, {"name": "GOOGLESHEETS_GET_SPREADSHEET_INFO", "description": "Get spreadsheet information"}, {"name": "GOOGLESHEETS_INSERT_DIMENSION", "description": "Insert rows or columns"}, {"name": "GOOGLESHEETS_SET_BASIC_FILTER", "description": "Set basic filter on a sheet"}, {"name": "GOOGLESHEETS_SPREADSHEETS_VALUES_APPEND", "description": "Append values to a spreadsheet"}, {"name": "GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_CLEAR", "description": "Clear multiple ranges in batch"}, {"name": "GOOGLESHEETS_SPREADSHEETS_VALUES_BATCH_GET_BY_DATA_FILTER", "description": "Get multiple ranges by filter"}, {"name": "GOOGLESHEETS_UPDATE_SPREADSHEET_PROPERTIES", "description": "Update spreadsheet properties"}, {"name": "GOOGLESHEETS_BATCH_GET", "description": "Get multiple ranges in batch"}, {"name": "GOOGLESHEETS_BATCH_UPDATE", "description": "Update multiple ranges in batch"}, {"name": "GOOGLESHEETS_BATCH_UPDATE_VALUES_BY_DATA_FILTER", "description": "Update values by data filter"}, {"name": "GOOGLESHEETS_CLEAR_VALUES", "description": "Clear values from a range"}, {"name": "GOOGLESHEETS_CREATE_GOOGLE_SHEET1", "description": "Create a new Google Sheet"}, {"name": "GOOGLESHEETS_FIND_WORKSHEET_BY_TITLE", "description": "Find a worksheet by its title"}, {"name": "GOOGLESHEETS_FORMAT_CELL", "description": "Format cells in a sheet"}, {"name": "GOOGLESHEETS_SEARCH_SPREADSHEETS", "description": "Search for spreadsheets"}]}, "airtable": {"integration_id": "9d09f222-f810-48f7-8614-43e1ae6b930f", "server_id": "0852d0de-da9f-4247-a351-3446f37382e1", "name": "Airtable", "description": "Connect to Airtable for database management and workflow automation", "tools": [{"name": "AIRTABLE_LIST_BASES", "description": "List all Airtable bases"}, {"name": "AIRTABLE_CREATE_BASE", "description": "Create a new Airtable base"}, {"name": "AIRTABLE_CREATE_COMMENT", "description": "Create a comment on a record"}, {"name": "AIRTABLE_CREATE_FIELD", "description": "Create a new field in a table"}, {"name": "AIRTABLE_CREATE_MULTIPLE_RECORDS", "description": "Create multiple records in batch"}, {"name": "AIRTABLE_CREATE_RECORD", "description": "Create a new record in Airtable"}, {"name": "AIRTABLE_CREATE_TABLE", "description": "Create a new table in a base"}, {"name": "AIRTABLE_DELETE_COMMENT", "description": "Delete a comment from a record"}, {"name": "AIRTABLE_DELETE_MULTIPLE_RECORDS", "description": "Delete multiple records in batch"}, {"name": "AIRTABLE_DELETE_RECORD", "description": "Delete a record from Airtable"}, {"name": "AIRTABLE_GET_BASE_SCHEMA", "description": "Get the schema of an Airtable base"}, {"name": "AIRTABLE_GET_RECORD", "description": "Get a specific record from Airtable"}, {"name": "AIRTABLE_GET_USER_INFO", "description": "Get information about an Airtable user"}, {"name": "AIRTABLE_LIST_COMMENTS", "description": "List comments on a record"}, {"name": "AIRTABLE_LIST_RECORDS", "description": "List records from an Airtable table"}, {"name": "AIRTABLE_UPDATE_MULTIPLE_RECORDS", "description": "Update multiple records in batch"}, {"name": "AIRTABLE_UPDATE_RECORD", "description": "Update a record in Airtable"}]}, "reddit": {"integration_id": "e429744a-5c7f-4415-b3c0-dfa8ffffa8cd", "server_id": "e45cad71-a560-4e7d-bd65-7ca8b4f74646", "name": "Reddit", "description": "Connect to Reddit for content management and community engagement", "tools": [{"name": "REDDIT_EDIT_REDDIT_COMMENT_OR_POST", "description": "Edit a comment or post on Reddit"}, {"name": "REDDIT_CREATE_REDDIT_POST", "description": "Create a new post on Reddit"}, {"name": "REDDIT_DELETE_REDDIT_COMMENT", "description": "Delete a comment on Reddit"}, {"name": "REDDIT_DELETE_REDDIT_POST", "description": "Delete a post on Reddit"}, {"name": "REDDIT_GET_USER_FLAIR", "description": "Get user flair in a subreddit"}, {"name": "REDDIT_POST_REDDIT_COMMENT", "description": "Post a comment on Reddit"}, {"name": "REDDIT_RETRIEVE_POST_COMMENTS", "description": "Retrieve comments from a Reddit post"}, {"name": "REDDIT_RETRIEVE_REDDIT_POST", "description": "Retrieve a specific Reddit post"}, {"name": "REDDIT_RETRIEVE_SPECIFIC_COMMENT", "description": "Retrieve a specific comment on Reddit"}, {"name": "REDDIT_SEARCH_ACROSS_SUBREDDITS", "description": "Search across multiple subreddits"}]}, "outlook": {"integration_id": "43596444-e972-42f0-b205-1b1645f6ff31", "server_id": "f64c7f49-a72a-414e-baa5-81d32e04cdee", "name": "Outlook", "description": "Connect to Outlook for email management and calendar integration", "tools": [{"name": "OUTLOOK_DOWNLOAD_OUTLOOK_ATTACHMENT", "description": "Download an attachment from Outlook"}, {"name": "OUTLOOK_OUTLOOK_CALENDAR_CREATE_EVENT", "description": "Create a calendar event in Outlook"}, {"name": "OUTLOOK_OUTLOOK_CREATE_CONTACT", "description": "Create a new contact in Outlook"}, {"name": "OUTLOOK_OUTLOOK_CREATE_DRAFT", "description": "Create an email draft in Outlook"}, {"name": "OUTLOOK_OUTLOOK_GET_CONTACT", "description": "Get a contact from Outlook"}, {"name": "OUTLOOK_OUTLOOK_GET_EVENT", "description": "Get a calendar event from Outlook"}, {"name": "OUTLOOK_OUTLOOK_GET_PROFILE", "description": "Get user profile from Outlook"}, {"name": "OUTLOOK_OUTLOOK_LIST_EVENTS", "description": "List calendar events in Outlook"}, {"name": "OUTLOOK_OUTLOOK_LIST_MESSAGES", "description": "List email messages in Outlook"}, {"name": "OUTLOOK_OUTLOOK_REPLY_EMAIL", "description": "Reply to an email in Outlook"}, {"name": "OUTLOOK_OUTLOOK_SEND_EMAIL", "description": "Send an email via Outlook"}, {"name": "OUTLOOK_OUTLOOK_UPDATE_EMAIL", "description": "Update an email in Outlook"}, {"name": "OUTLOOK_LIST_OUTLOOK_ATTACHMENTS", "description": "List attachments in an Outlook email"}, {"name": "OUTLOOK_OUTLOOK_CREATE_DRAFT_REPLY", "description": "Create a draft reply in Outlook"}, {"name": "OUTLOOK_OUTLOOK_DELETE_CONTACT", "description": "Delete a contact from Outlook"}, {"name": "OUTLOOK_OUTLOOK_DELETE_EVENT", "description": "Delete a calendar event from Outlook"}, {"name": "OUTLOOK_OUTLOOK_GET_MESSAGE", "description": "Get a specific email message from Outlook"}, {"name": "OUTLOOK_OUTLOOK_GET_SCHEDULE", "description": "Get schedule information from Outlook"}, {"name": "OUTLOOK_OUTLOOK_LIST_CONTACTS", "description": "List contacts in Outlook"}, {"name": "OUTLOOK_OUTLOOK_SEARCH_MESSAGES", "description": "Search for messages in Outlook"}, {"name": "OUTLOOK_OUTLOOK_UPDATE_CALENDAR_EVENT", "description": "Update a calendar event in Outlook"}, {"name": "OUTLOOK_OUTLOOK_UPDATE_CONTACT", "description": "Update a contact in Outlook"}]}, "hubspot": {"integration_id": "************************************", "server_id": "a59f5fd7-5f18-41db-84f4-37b9ea5cec72", "name": "HubSpot", "description": "Connect to HubSpot for CRM and marketing automation", "tools": [{"name": "HUBSPOT_CREATE_PRODUCT_BATCH", "description": "Create multiple products in batch"}, {"name": "HUBSPOT_CREATE_PRODUCT_OBJECT", "description": "Create a new product in HubSpot"}, {"name": "HUBSPOT_LIST_PRODUCTS_WITH_PAGING", "description": "List products with pagination"}, {"name": "HUBSPOT_UPDATE_PRODUCT", "description": "Update a product in HubSpot"}, {"name": "HUBSPOT_CAMPAIGN_SEARCH", "description": "Search for campaigns in HubSpot"}, {"name": "HUBSPOT_CREATE_A_BATCH_OF_COMPANIES", "description": "Create multiple companies in batch"}, {"name": "HUBSPOT_CREATE_A_BATCH_OF_CONTACTS", "description": "Create multiple contacts in batch"}, {"name": "HUBSPOT_CREATE_A_BATCH_OF_EMAILS", "description": "Create multiple emails in batch"}, {"name": "HUBSPOT_CREATE_BATCH_OF_DEALS", "description": "Create multiple deals in batch"}, {"name": "HUBSPOT_CREATE_WORKFLOW", "description": "Create a workflow in HubSpot"}, {"name": "HUBSPOT_CUSTOMIZABLE_CONTACTS_PAGE_RETRIEVAL", "description": "Retrieve contacts with custom filters"}, {"name": "HUBSPOT_FETCH_CONTACT_DETAILS_BY_ID", "description": "Get contact details by ID"}, {"name": "HUBSPOT_FETCH_CONTACT_IDS", "description": "Fetch contact IDs from HubSpot"}, {"name": "HUBSPOT_GET_ALL_WORKFLOWS", "description": "Get all workflows in HubSpot"}, {"name": "HUBSPOT_GET_CAMPAIGN_METRICS", "description": "Get campaign performance metrics"}, {"name": "HUBSPOT_LIST", "description": "List objects in HubSpot"}, {"name": "HUBSPOT_PARTIALLY_UPDATE_CONTACT_USING_CONTACT_ID", "description": "Partially update a contact"}, {"name": "HUBSPOT_UPDATE", "description": "Update objects in HubSpot"}, {"name": "HUBSPOT_UPDATE_A_BATCH_OF_CAMPAIGNS", "description": "Update multiple campaigns in batch"}, {"name": "HUBSPOT_UPDATE_A_BATCH_OF_CONTACTS", "description": "Update multiple contacts in batch"}, {"name": "HUBSPOT_UPDATE_A_BATCH_OF_EMAILS", "description": "Update multiple emails in batch"}]}, "zoom": {"integration_id": "2125778c-3c49-4943-bd25-2d61912f39f4", "server_id": "a3779748-a802-4d27-9656-bd514f6f90a2", "name": "Zoom", "description": "Connect to Zoom for video conferencing and meeting management", "tools": [{"name": "ZOOM_CREATE_A_MEETING", "description": "Create a new Zoom meeting"}, {"name": "ZOOM_GET_A_MEETING", "description": "Get details of a Zoom meeting"}, {"name": "ZOOM_GET_A_MEETING_SUMMARY", "description": "Get summary of a Zoom meeting"}, {"name": "ZOOM_GET_MEETING_RECORDINGS", "description": "Get recordings of a Zoom meeting"}, {"name": "ZOOM_LIST_ALL_RECORDINGS", "description": "List all Zoom recordings"}, {"name": "ZOOM_LIST_MEETINGS", "description": "List all Zoom meetings"}, {"name": "ZOOM_UPDATE_MEETING_STATUS", "description": "Update status of a Zoom meeting"}, {"name": "ZOOM_ADD_A_MEETING_REGISTRANT", "description": "Add a registrant to a Zoom meeting"}, {"name": "ZOOM_GET_MEETING_INVITATION", "description": "Get invitation text for a Zoom meeting"}, {"name": "ZOOM_GET_MEETING_RECORDING_S_ANALYTICS_SUMMARY", "description": "Get analytics for meeting recordings"}]}, "salesforce": {"integration_id": "acb1a404-7fd5-4a4d-9c95-f5a008c6a00a", "server_id": "709bb788-3482-4e71-9d58-a265519ec768", "name": "Salesforce", "description": "Connect to Salesforce for CRM and sales automation", "tools": [{"name": "SALESFORCE_ACCOUNT_CREATION_WITH_CONTENT_TYPE_OPTION", "description": "Create a Salesforce account"}, {"name": "SALESFORCE_CREATE_CAMPAIGN_RECORD_VIA_POST", "description": "Create a campaign in Salesforce"}, {"name": "SALESFORCE_CREATE_LEAD_WITH_SPECIFIED_CONTENT_TYPE", "description": "Create a lead in Salesforce"}, {"name": "SALESFORCE_CREATE_NOTE_RECORD_WITH_CONTENT_TYPE_HEADER", "description": "Create a note in Salesforce"}, {"name": "SALESFORCE_EXECUTE_SOQL_QUERY", "description": "Execute SOQL query in Salesforce"}, {"name": "SALESFORCE_FETCH_MODIFIED_OR_UNMODIFIED_SOBJECTS", "description": "Fetch modified objects from Salesforce"}, {"name": "SALESFORCE_QUERY_CONTACTS_BY_NAME", "description": "Query contacts by name in Salesforce"}, {"name": "SALESFORCE_REMOVE_ACCOUNT_BY_UNIQUE_IDENTIFIER", "description": "Delete an account in Salesforce"}, {"name": "SALESFORCE_RETRIEVE_ACCOUNT_DATA_AND_ERROR_RESPONSES", "description": "Retrieve account data from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_CAMPAIGN_DATA_WITH_ERROR_HANDLING", "description": "Retrieve campaign data from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_LEAD_BY_ID", "description": "Get a lead by <PERSON> from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_LEAD_DATA_WITH_VARIOUS_RESPONSES", "description": "Retrieve lead data from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_NOTE_WITH_CONDITIONS", "description": "Retrieve notes from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_OPPORTUNITIES_DATA", "description": "Retrieve opportunities from Salesforce"}, {"name": "SALESFORCE_RETRIEVE_SPECIFIC_CONTACT_BY_ID", "description": "Get a contact by ID from Salesforce"}, {"name": "SALESFORCE_UPDATE_CONTACT_BY_ID", "description": "Update a contact in Salesforce"}]}, "google_drive": {"integration_id": "98942f5c-ec67-4ad8-864c-42a34490b0ec", "server_id": "b3cff4ab-7ce1-451d-a8de-94881d315279", "name": "Google Drive", "description": "Connect to Google Drive for file storage and collaboration", "tools": [{"name": "GOOGLEDRIVE_CREATE_COMMENT", "description": "Create a comment on a file"}, {"name": "GOOGLEDRIVE_CREATE_DRIVE", "description": "Create a new shared drive"}, {"name": "GOOGLEDRIVE_CREATE_FILE", "description": "Create a new file in Drive"}, {"name": "GOOGLEDRIVE_CREATE_FOLDER", "description": "Create a new folder in Drive"}, {"name": "GOOGLEDRIVE_DOWNLOAD_FILE", "description": "Download a file from Drive"}, {"name": "GOOGLEDRIVE_GET_PERMISSION", "description": "Get file permissions"}, {"name": "GOOGLEDRIVE_GET_REPLY", "description": "Get replies to comments"}, {"name": "GOOGLEDRIVE_LIST_CHANGES", "description": "List changes in Drive"}, {"name": "GOOGLEDRIVE_LIST_FILES", "description": "List files in Drive"}, {"name": "GOOGLEDRIVE_LIST_SHARED_DRIVES", "description": "List shared drives"}, {"name": "GOOGLEDRIVE_MOVE_FILE", "description": "Move a file to another location"}, {"name": "GOOGLEDRIVE_PARSE_FILE", "description": "Parse file content"}, {"name": "GOOGLEDRIVE_UPLOAD_FILE", "description": "Upload a file to Drive"}, {"name": "GOOGLEDRIVE_GENERATE_IDS", "description": "Generate file IDs"}, {"name": "GOOGLEDRIVE_COPY_FILE", "description": "Copy a file in Drive"}, {"name": "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT", "description": "Create a file from text content"}, {"name": "GOOGLEDRIVE_EDIT_FILE", "description": "Edit a file in Drive"}, {"name": "GOOGLEDRIVE_FIND_FILE", "description": "Find a file in Drive"}, {"name": "GOOGLEDRIVE_FIND_FOLDER", "description": "Find a folder in Drive"}, {"name": "GOOGLEDRIVE_GOOGLE_DRIVE_DELETE_FOLDER_OR_FILE_ACTION", "description": "Delete a file or folder"}]}, "clickup": {"integration_id": "77d8df8e-f5c7-417b-bb54-0f9215f44b73", "server_id": "5e928d2b-7c22-418a-92aa-dbf7092fca35", "name": "ClickUp", "description": "Connect to ClickUp for project management and task tracking", "tools": [{"name": "CLICKUP-UPDATE-TASK", "description": "Update a task in ClickUp"}, {"name": "CLICKUP-UPDATE-TASK-CUSTOM-FIELD", "description": "Update a custom field on a task"}, {"name": "CLICKUP-UPDATE-SPACE", "description": "Update a space in ClickUp"}, {"name": "CLICKUP-UPDATE-LIST", "description": "Update a list in ClickUp"}, {"name": "CLICKUP-UPDATE-FOLDER", "description": "Update a folder in ClickUp"}, {"name": "CLICKUP-UPDATE-COMMENT", "description": "Update a comment in ClickUp"}, {"name": "CLICKUP-UPDATE-CHECKLIST", "description": "Update a checklist in ClickUp"}, {"name": "CLICKUP-UPDATE-CHECKLIST-ITEM", "description": "Update a checklist item in ClickUp"}, {"name": "CLICKUP-STOP-TIME-ENTRY", "description": "Stop a time entry in ClickUp"}, {"name": "CLICKUP-START-TIME-ENTRY", "description": "Start a time entry in ClickUp"}, {"name": "CLICKUP-REMOVE-TASK-CUSTOM-FIELD", "description": "Remove a custom field from a task"}, {"name": "CLICKUP-GET-VIEW", "description": "Get a view in ClickUp"}, {"name": "CLICKUP-GET-VIEW-TASKS", "description": "Get tasks from a view in ClickUp"}, {"name": "CLICKUP-GET-VIEW-COMMENTS", "description": "Get comments from a view in ClickUp"}, {"name": "CLICKUP-GET-TEAM-VIEWS", "description": "Get team views in ClickUp"}, {"name": "CLICKUP-GET-TASKS", "description": "Get tasks from ClickUp"}, {"name": "CLICKUP-GET-TASK", "description": "Get details of a task"}, {"name": "CLICKUP-GET-TASK-TEMPLATES", "description": "Get task templates in ClickUp"}, {"name": "CLICKUP-GET-TASK-COMMENTS", "description": "Get comments from a task"}, {"name": "CLICKUP-GET-SPACES", "description": "Get spaces in ClickUp"}, {"name": "CLICKUP-GET-SPACE", "description": "Get details of a space"}, {"name": "CLICKUP-GET-SPACE-VIEWS", "description": "Get views from a space"}, {"name": "CLICKUP-GET-LISTS", "description": "Get lists in ClickUp"}, {"name": "CLICKUP-GET-LIST", "description": "Get details of a list"}, {"name": "CLICKUP-GET-LIST-VIEWS", "description": "Get views from a list"}, {"name": "CLICKUP-GET-LIST-COMMENTS", "description": "Get comments from a list"}, {"name": "CLICKUP-GET-FOLDERS", "description": "Get folders in ClickUp"}, {"name": "CLICKUP-GET-FOLDER", "description": "Get details of a folder"}, {"name": "CLICKUP-GET-FOLDER-VIEWS", "description": "Get views from a folder"}, {"name": "CLICKUP-GET-CUSTOM-FIELDS", "description": "Get custom fields in ClickUp"}, {"name": "CLICKUP-DELETE-TASK", "description": "Delete a task from ClickUp"}, {"name": "CLICKUP-DELETE-SPACE", "description": "Delete a space from ClickUp"}, {"name": "CLICKUP-DELETE-LIST", "description": "Delete a list from ClickUp"}, {"name": "CLICKUP-DELETE-FOLDER", "description": "Delete a folder from ClickUp"}, {"name": "CLICKUP-DELETE-COMMENT", "description": "Delete a comment from ClickUp"}, {"name": "CLICKUP-DELETE-CHECKLIST", "description": "Delete a checklist from ClickUp"}, {"name": "CLICKUP-DELETE-CHECKLIST-ITEM", "description": "Delete a checklist item from ClickUp"}, {"name": "CLICKUP-CREATE-VIEW-COMMENT", "description": "Create a comment on a view"}, {"name": "CLICKUP-CREATE-THREADED-COMMENT", "description": "Create a threaded comment in ClickUp"}, {"name": "CLICKUP-CREATE-TASK", "description": "Create a new task in ClickUp"}, {"name": "CLICKUP-CREATE-TASK-FROM-TEMPLATE", "description": "Create a task from a template"}, {"name": "CLICKUP-CREATE-TASK-COMMENT", "description": "Create a comment on a task"}, {"name": "CLICKUP-CREATE-SPACE", "description": "Create a new space in ClickUp"}, {"name": "CLICKUP-CREATE-LIST", "description": "Create a new list in ClickUp"}, {"name": "CLICKUP-CREATE-LIST-COMMENT", "description": "Create a comment on a list"}, {"name": "CLICKUP-CREATE-FOLDER", "description": "Create a new folder in ClickUp"}, {"name": "CLICKUP-CREATE-CHECKLIST", "description": "Create a checklist in ClickUp"}, {"name": "CLICKUP-CREATE-CHECKLIST-ITEM", "description": "Create a checklist item in ClickUp"}, {"name": "CLICKUP-CREATE-CHAT-VIEW-COMMENT", "description": "Create a comment in chat view"}]}}