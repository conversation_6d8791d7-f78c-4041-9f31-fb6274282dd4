"""
Dynamic Tool Prompt Module

Simple, focused system that recommends specific tools and execution order
based on user's message, chat context, and available tool configurations.
"""

import os
import json
from typing import Optional, Dict, List, Any
from utils.logger import logger


def get_dynamic_tool_guidance(thread_id: str = None, user_id: str = None) -> str:
    """
    Synchronous entry point for backward compatibility.
    In async contexts, use get_dynamic_tool_guidance_async instead.
    """
    # This is now just a stub that returns empty string
    # The actual work is done by pre-computing in run_agent
    logger.debug(
        "Sync get_dynamic_tool_guidance called - returning empty (use async version)"
    )
    return ""


async def get_dynamic_tool_guidance_async(
    thread_id: str, user_id: str, db_client: Any = None
) -> str:
    """
    Async version that can be called from within existing event loop.

    Args:
        thread_id: Thread ID to analyze
        user_id: User ID for tool context
        db_client: Optional pre-initialized database client to reuse

    Returns:
        Tool guidance string for system prompt injection
    """
    if not thread_id or not user_id:
        return ""

    try:
        # Get OpenAI API key
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            logger.info("❌ OpenAI API key not found, skipping dynamic tool guidance")
            return ""

        # Generate guidance using existing async functions
        guidance = await _generate_tool_guidance(
            thread_id, user_id, openai_api_key, db_client
        )
        return guidance

    except Exception as e:
        logger.error(f"Error generating dynamic tool guidance: {e}")
        return ""


async def _generate_tool_guidance(
    thread_id: str, user_id: str, openai_api_key: str, db_client: Any = None
) -> str:
    """Generate tool guidance using GPT-4o mini analysis."""
    try:
        logger.info("🚀 Starting dynamic tool guidance generation")

        # Get all required context
        user_message = await _get_latest_user_message(thread_id, db_client)
        logger.info("📝 Retrieved user message")

        chat_summary = await _get_chat_summary(thread_id, db_client)
        logger.info("💬 Generated chat summary")

        user_tool_context = await _get_user_tool_context(user_id, db_client)
        logger.info("🔧 Loaded user tool context with descriptions")

        if not user_message:
            logger.info("❌ No user message found, skipping guidance")
            return ""

        # Analyze with GPT-4o mini
        logger.info("🤖 Calling GPT-4o mini for tool analysis")
        analysis = await _analyze_with_gpt4o_mini(
            user_message=user_message,
            chat_summary=chat_summary,
            tool_context=user_tool_context,
            openai_api_key=openai_api_key,
        )

        if not analysis:
            logger.info("❌ GPT-4o mini analysis failed")
            return ""

        logger.info("✅ GPT-4o mini analysis completed")

        # Format for prompt injection
        guidance = _format_guidance(analysis)
        if guidance:
            logger.info("✨ Dynamic tool guidance injected into prompt")

        return guidance

    except Exception as e:
        logger.error(f"Error in tool guidance generation: {e}")
        return ""


async def _get_latest_user_message(thread_id: str, db_client: Any = None) -> str:
    """Get the latest user message from the thread."""
    try:
        # Use provided client or create new one
        if db_client:
            client = db_client
        else:
            from services.supabase import DBConnection

            db = DBConnection()
            client = await db.client

        result = (
            await client.table("messages")
            .select("content")
            .eq("thread_id", thread_id)
            .eq("type", "user")
            .order("created_at", desc=True)
            .limit(1)
            .execute()
        )

        if not result.data:
            return ""

        content = result.data[0]["content"]

        # Parse JSON content if needed
        if isinstance(content, str):
            try:
                content_obj = json.loads(content)
                content = content_obj.get("content", content)
            except json.JSONDecodeError:
                pass

        return str(content) if content else ""

    except Exception as e:
        logger.error(f"Error getting latest user message: {e}")
        return ""


async def _get_chat_summary(thread_id: str, db_client: Any = None) -> str:
    """Get chat summary - lifted from run.py pattern."""
    try:
        # Use provided client or create new one
        if db_client:
            client = db_client
        else:
            from services.supabase import DBConnection

            db = DBConnection()
            client = await db.client

        # Get recent messages for summary
        messages = (
            await client.table("messages")
            .select("content, type, created_at")
            .eq("thread_id", thread_id)
            .order("created_at", desc=True)
            .limit(10)
            .execute()
        )

        if not messages.data:
            return "New conversation"

        # Create simple summary
        user_messages = []
        assistant_messages = []

        for msg in messages.data:
            content = msg.get("content", "")
            msg_type = msg.get("type", "user")

            # Parse JSON content
            if isinstance(content, str):
                try:
                    content_obj = json.loads(content)
                    content = content_obj.get("content", content)
                except:
                    pass

            # Truncate long messages
            if len(str(content)) > 200:
                content = str(content)[:197] + "..."

            if msg_type == "user":
                user_messages.append(str(content))
            else:
                assistant_messages.append(str(content))

        summary = f"Conversation: {len(user_messages)} user messages, {len(assistant_messages)} assistant responses"
        if user_messages:
            latest_context = user_messages[0]  # Most recent first due to desc order
            summary += f"\nLatest context: {latest_context}"

        return summary

    except Exception as e:
        logger.error(f"Error getting chat summary: {e}")
        return "Error retrieving chat context"


async def _get_user_tool_context(user_id: str, db_client: Any = None) -> Dict:
    """Get user's configured tools with descriptions from composio_mcp_servers.json."""
    try:
        # Use provided client or create new one
        if db_client:
            client = db_client
        else:
            from services.supabase import DBConnection

            db = DBConnection()
            client = await db.client

        # Get user's configured MCPs
        agent_result = (
            await client.table("agents")
            .select("custom_mcps")
            .eq("account_id", user_id)
            .eq("is_default", True)
            .execute()
        )

        if not agent_result.data:
            return {"configured_tools": {}}

        custom_mcps = agent_result.data[0].get("custom_mcps", [])
        if not isinstance(custom_mcps, list):
            return {"configured_tools": {}}

        # Load tool descriptions from composio_mcp_servers.json
        tool_descriptions = _load_tool_descriptions()

        # Build user's tool context with descriptions
        configured_tools = {}

        for mcp in custom_mcps:
            app_key = mcp.get("app_key")
            enabled_tools = mcp.get("enabledTools", [])

            if app_key and app_key in tool_descriptions:
                app_info = tool_descriptions[app_key]

                # Get descriptions for enabled tools
                tool_details = []
                for tool_name in enabled_tools:
                    for tool in app_info.get("tools", []):
                        if tool["name"] == tool_name:
                            tool_details.append(
                                {"name": tool_name, "description": tool["description"]}
                            )
                            break

                if tool_details:
                    configured_tools[app_key] = {
                        "app_name": app_info.get("name", app_key),
                        "app_description": app_info.get("description", ""),
                        "enabled_tools": tool_details,
                    }

        return {"configured_tools": configured_tools}

    except Exception as e:
        logger.error(f"Error getting user tool context: {e}")
        return {"configured_tools": {}}


def _load_tool_descriptions() -> Dict:
    """Load tool descriptions from composio_mcp_servers.json."""
    try:
        constants_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "composio_devtools",
            "composio_mcp_servers.json",
        )

        with open(constants_file, "r") as f:
            return json.load(f)

    except Exception as e:
        logger.error(f"Error loading tool descriptions: {e}")
        return {}


async def _analyze_with_gpt4o_mini(
    user_message: str, chat_summary: str, tool_context: Dict, openai_api_key: str
) -> Optional[Dict]:
    """Analyze task with GPT-4o mini to determine specific tools and execution order."""
    try:
        import httpx

        configured_tools = tool_context.get("configured_tools", {})

        # Build concise system prompt
        system_prompt = f"""You are Atlas's Tool Execution Planner. Analyze the user's task and recommend specific tools in execution order.

# AVAILABLE TOOLS
{json.dumps(configured_tools, indent=2)}

# YOUR JOB
1. Identify which specific tools are needed for this task
2. Determine the optimal execution order
3. Provide brief reasoning for each tool choice

# OUTPUT FORMAT (JSON)
{{
  "recommended_execution": [
    {{
      "step": 1,
      "app": "app_key",
      "tool": "TOOL_NAME",
      "purpose": "What this tool accomplishes",
      "reasoning": "Why this tool at this step"
    }}
  ],
  "task_assessment": {{
    "complexity": "simple|medium|complex",
    "estimated_steps": 3,
    "requires_sequence": true/false,
    "success_criteria": "How to measure success"
  }}
}}

# GUIDELINES
- Only recommend tools the user actually has configured
- Be specific about tool names and execution order
- Keep reasoning concise but clear
- If no tools are needed, return empty recommended_execution array
- Focus on efficiency and logical flow"""

        user_prompt = f"""# TASK TO ANALYZE
**User Message:** {user_message}

**Chat Context:** {chat_summary}

Based on the user's configured tools and this specific task, what tools should be used and in what order?

Focus on:
1. Which specific tools from their configured apps
2. Optimal execution sequence
3. Clear purpose for each tool"""

        # Call GPT-4o mini
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {openai_api_key}",
                    "Content-Type": "application/json",
                },
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt},
                    ],
                    "temperature": 0.1,
                    "max_tokens": 1000,
                    "response_format": {"type": "json_object"},
                },
            )

        if response.status_code != 200:
            logger.error(f"GPT-4o mini API error: {response.status_code}")
            return None

        result = response.json()
        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")

        try:
            analysis = json.loads(content)
            logger.info("GPT-4o mini tool analysis completed")
            return analysis
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse GPT response: {e}")
            return None

    except Exception as e:
        logger.error(f"Error in GPT-4o mini analysis: {e}")
        return None


def _format_guidance(analysis: Dict) -> str:
    """Format analysis into guidance string for prompt injection."""
    try:
        recommended_execution = analysis.get("recommended_execution", [])
        task_assessment = analysis.get("task_assessment", {})

        # Debug logging
        logger.debug(f"GPT-4o mini analysis result: {json.dumps(analysis, indent=2)}")

        if not recommended_execution:
            logger.info(
                "📊 No tool recommendations from GPT-4o mini (task may not require specific tools)"
            )
            return ""

        # Format execution plan
        guidance = "\n\n# 🎯 TOOL EXECUTION PLAN\n\n"

        complexity = task_assessment.get("complexity", "unknown")
        estimated_steps = task_assessment.get(
            "estimated_steps", len(recommended_execution)
        )

        guidance += (
            f"**Task Complexity:** {complexity.title()} ({estimated_steps} steps)\n\n"
        )

        guidance += "## Recommended Tool Sequence:\n\n"

        for execution_step in recommended_execution:
            step = execution_step.get("step", 1)
            app = execution_step.get("app", "unknown")
            tool = execution_step.get("tool", "unknown")
            purpose = execution_step.get("purpose", "")
            reasoning = execution_step.get("reasoning", "")

            guidance += f"**Step {step}: {app.title()}**\n"
            guidance += f"- Tool: `{tool}`\n"
            guidance += f"- Purpose: {purpose}\n"
            guidance += f"- Reasoning: {reasoning}\n\n"

        # Add success criteria if available
        success_criteria = task_assessment.get("success_criteria")
        if success_criteria:
            guidance += f"**Success Criteria:** {success_criteria}\n\n"

        guidance += (
            "**Execution Notes:** Follow this sequence for optimal task completion.\n"
        )

        return guidance

    except Exception as e:
        logger.error(f"Error formatting guidance: {e}")
        return "\n\n# 🎯 TOOL GUIDANCE\nAnalysis completed - check logs for details\n"
